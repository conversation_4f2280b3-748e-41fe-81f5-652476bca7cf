Option Explicit

'==============================================================================
' 魔法模拟器控制面板
' 提供统一的用户界面来访问所有模拟器功能
'==============================================================================

' 主控制面板
Public Sub ShowMagicSimulatorControlPanel()
    Dim choice As String
    
    Do
        choice = InputBox( _
            "=== 高级魔法合成模拟器控制面板 ===" & vbCrLf & vbCrLf & _
            "请选择要执行的操作:" & vbCrLf & vbCrLf & _
            "【主要功能】" & vbCrLf & _
            "1 - 运行完整模拟（推荐）" & vbCrLf & _
            "2 - 快速测试（10次模拟）" & vbCrLf & _
            "3 - 自定义模拟" & vbCrLf & vbCrLf & _
            "【演示功能】" & vbCrLf & _
            "4 - 基础模拟演示" & vbCrLf & _
            "5 - 不同初始条件对比" & vbCrLf & _
            "6 - 合成路径分析" & vbCrLf & _
            "7 - 实时模拟观察" & vbCrLf & vbCrLf & _
            "【工具功能】" & vbCrLf & _
            "8 - 分析当前魔法池" & vbCrLf & _
            "9 - 创建预设魔法池" & vbCrLf & _
            "10 - 显示合成规则" & vbCrLf & _
            "11 - 运行系统测试" & vbCrLf & _
            "12 - 验证合成规则" & vbCrLf & vbCrLf & _
            "0 - 退出" & vbCrLf & vbCrLf & _
            "请输入选项编号:", _
            "魔法模拟器控制面板", "1")
        
        If choice = "" Or choice = "0" Then
            Exit Do
        End If
        
        Select Case choice
            Case "1"
                Call ExecuteMainSimulation
            Case "2"
                Call ExecuteQuickTest
            Case "3"
                Call ExecuteCustomSimulation
            Case "4"
                Call Demo1_BasicSimulation
            Case "5"
                Call Demo2_CompareInitialConditions
            Case "6"
                Call Demo3_SynthesisPathAnalysis
            Case "7"
                Call Demo4_RealTimeSimulation
            Case "8"
                Call AnalyzeMagicPool
            Case "9"
                Call CreatePresetMagicPool
            Case "10"
                Call ShowSynthesisRules
            Case "11"
                Call RunAllTests
            Case "12"
                Call ValidateSynthesisRules
            Case Else
                MsgBox "无效的选项，请重新选择。", vbExclamation
        End Select
        
    Loop
    
    MsgBox "感谢使用高级魔法合成模拟器！", vbInformation
End Sub

' 执行主要模拟
Private Sub ExecuteMainSimulation()
    Dim simulationCount As String
    Dim initialMagicCount As String
    
    simulationCount = InputBox( _
        "请输入模拟次数:" & vbCrLf & vbCrLf & _
        "建议值:" & vbCrLf & _
        "• 快速测试: 100-500" & vbCrLf & _
        "• 标准分析: 1000-5000" & vbCrLf & _
        "• 精确分析: 10000+" & vbCrLf & vbCrLf & _
        "注意: 模拟次数越多，结果越准确，但耗时也越长。", _
        "模拟配置", "1000")
    
    If simulationCount = "" Then Exit Sub
    
    initialMagicCount = InputBox( _
        "请输入每个玩家的初始T1魔法数量:" & vbCrLf & vbCrLf & _
        "建议值:" & vbCrLf & _
        "• 困难模式: 10-15" & vbCrLf & _
        "• 标准模式: 20-30" & vbCrLf & _
        "• 简单模式: 40-50" & vbCrLf & vbCrLf & _
        "注意: 初始魔法越多，成功率越高。", _
        "初始配置", "20")
    
    If initialMagicCount = "" Then Exit Sub
    
    If Not IsNumeric(simulationCount) Or Not IsNumeric(initialMagicCount) Then
        MsgBox "请输入有效的数字！", vbExclamation
        Exit Sub
    End If
    
    If CLng(simulationCount) <= 0 Or CLng(initialMagicCount) <= 0 Then
        MsgBox "数值必须大于0！", vbExclamation
        Exit Sub
    End If
    
    ' 确认执行
    Dim confirmMsg As String
    confirmMsg = "即将执行模拟:" & vbCrLf & vbCrLf & _
                "模拟次数: " & simulationCount & vbCrLf & _
                "初始魔法数量: " & initialMagicCount & vbCrLf & vbCrLf & _
                "预计耗时: " & EstimateTime(CLng(simulationCount)) & vbCrLf & vbCrLf & _
                "确定要开始吗？"
    
    If MsgBox(confirmMsg, vbYesNo + vbQuestion) = vbYes Then
        Call RunAdvancedMagicSimulationWithParams(CLng(simulationCount), CLng(initialMagicCount))
    End If
End Sub

' 执行快速测试
Private Sub ExecuteQuickTest()
    MsgBox "即将运行快速测试（10次模拟），用于验证系统功能。", vbInformation
    Call QuickTest
End Sub

' 执行自定义模拟
Private Sub ExecuteCustomSimulation()
    Dim config As String
    config = InputBox( _
        "自定义模拟配置:" & vbCrLf & vbCrLf & _
        "请选择预设配置:" & vbCrLf & _
        "1 - 高精度分析 (10000次, 25个初始魔法)" & vbCrLf & _
        "2 - 快速分析 (500次, 20个初始魔法)" & vbCrLf & _
        "3 - 困难模式测试 (1000次, 10个初始魔法)" & vbCrLf & _
        "4 - 简单模式测试 (1000次, 50个初始魔法)" & vbCrLf & _
        "5 - 极限测试 (100次, 5个初始魔法)" & vbCrLf & vbCrLf & _
        "请输入配置编号:", _
        "自定义模拟", "2")
    
    If config = "" Then Exit Sub
    
    Select Case config
        Case "1"
            Call RunAdvancedMagicSimulationWithParams(10000, 25)
        Case "2"
            Call RunAdvancedMagicSimulationWithParams(500, 20)
        Case "3"
            Call RunAdvancedMagicSimulationWithParams(1000, 10)
        Case "4"
            Call RunAdvancedMagicSimulationWithParams(1000, 50)
        Case "5"
            Call RunAdvancedMagicSimulationWithParams(100, 5)
        Case Else
            MsgBox "无效的配置选项！", vbExclamation
    End Select
End Sub

' 带参数运行模拟
Private Sub RunAdvancedMagicSimulationWithParams(simulationCount As Long, initialMagicCount As Long)
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    
    ' 初始化系统
    Call InitializeSimulationSystem
    
    ' 运行批量模拟
    Dim aggregatedResults As Object
    Set aggregatedResults = RunBatchSimulation(simulationCount, initialMagicCount)
    
    ' 输出结果
    Call OutputSimulationResults(aggregatedResults, simulationCount)
    
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    
    ' 显示结果摘要
    Dim summary As String
    summary = "模拟完成！" & vbCrLf & vbCrLf & _
             "模拟次数: " & simulationCount & vbCrLf & _
             "成功次数: " & aggregatedResults("SuccessCount") & vbCrLf & _
             "成功率: " & Format(aggregatedResults("SuccessCount") / simulationCount, "0.00%") & vbCrLf & _
             "平均合成轮数: " & Format(aggregatedResults("TotalIterations") / simulationCount, "0.0") & vbCrLf & vbCrLf & _
             "详细结果已输出到统计数据工作表。"
    
    MsgBox summary, vbInformation, "模拟结果摘要"
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    MsgBox "模拟过程中发生错误: " & Err.Description & " (错误号: " & Err.Number & ")", vbCritical
End Sub

' 估算执行时间
Private Function EstimateTime(simulationCount As Long) As String
    Dim seconds As Double
    
    ' 基于经验的时间估算（每1000次模拟约需10-30秒，取决于计算机性能）
    seconds = simulationCount / 1000 * 20
    
    If seconds < 60 Then
        EstimateTime = Format(seconds, "0") & " 秒"
    ElseIf seconds < 3600 Then
        EstimateTime = Format(seconds / 60, "0.0") & " 分钟"
    Else
        EstimateTime = Format(seconds / 3600, "0.0") & " 小时"
    End If
End Function

' 显示帮助信息
Public Sub ShowHelp()
    Dim helpText As String
    helpText = "=== 高级魔法合成模拟器帮助 ===" & vbCrLf & vbCrLf & _
              "【主要功能说明】" & vbCrLf & _
              "• 运行完整模拟: 执行大批量模拟，获得准确的统计数据" & vbCrLf & _
              "• 快速测试: 运行少量模拟，验证系统功能" & vbCrLf & _
              "• 自定义模拟: 使用预设配置进行特定测试" & vbCrLf & vbCrLf & _
              "【演示功能说明】" & vbCrLf & _
              "• 基础模拟演示: 展示基本的模拟功能" & vbCrLf & _
              "• 不同初始条件对比: 分析初始魔法数量对成功率的影响" & vbCrLf & _
              "• 合成路径分析: 详细分析各阶级转换的频率" & vbCrLf & _
              "• 实时模拟观察: 观察单次模拟的详细过程" & vbCrLf & vbCrLf & _
              "【工具功能说明】" & vbCrLf & _
              "• 分析当前魔法池: 分析魔法模拟器工作表中的魔法池" & vbCrLf & _
              "• 创建预设魔法池: 创建标准的测试魔法池" & vbCrLf & _
              "• 显示合成规则: 查看已加载的合成规则" & vbCrLf & _
              "• 运行系统测试: 验证系统各组件功能" & vbCrLf & _
              "• 验证合成规则: 检查合成规则的完整性"
    
    MsgBox helpText, vbInformation, "帮助信息"
End Sub

' 显示关于信息
Public Sub ShowAbout()
    Dim aboutText As String
    aboutText = "=== 高级魔法合成模拟器 v1.0 ===" & vbCrLf & vbCrLf & _
               "开发日期: 2025年8月" & vbCrLf & _
               "兼容性: Excel 2016及以上版本" & vbCrLf & _
               "依赖: 需要启用VBA宏功能" & vbCrLf & vbCrLf & _
               "【主要特性】" & vbCrLf & _
               "• 完全自动化的魔法合成模拟" & vbCrLf & _
               "• 智能的合成策略算法" & vbCrLf & _
               "• 详细的统计分析功能" & vbCrLf & _
               "• 高性能的批量处理能力" & vbCrLf & _
               "• 丰富的演示和测试功能" & vbCrLf & vbCrLf & _
               "【技术架构】" & vbCrLf & _
               "• 基于Dictionary对象的高效数据结构" & vbCrLf & _
               "• 内存中的规则缓存机制" & vbCrLf & _
               "• 优化的合成算法" & vbCrLf & _
               "• 模块化的代码设计" & vbCrLf & vbCrLf & _
               "感谢使用本模拟器！"
    
    MsgBox aboutText, vbInformation, "关于"
End Sub
