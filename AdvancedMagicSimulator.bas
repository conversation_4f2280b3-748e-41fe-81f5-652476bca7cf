Option Explicit

'==============================================================================
' 高级魔法合成模拟器 v1.0
' 功能：完全自动化的魔法合成模拟，支持批量运行和详细统计
'==============================================================================

' 全局变量
Private g_SynthesisRules As Object      ' 合成规则字典
Private g_MagicTierMap As Object        ' 魔法等级映射
Private g_TierPriority As Object        ' 等级优先级
Private g_Statistics As Object          ' 统计数据

' 主入口函数
Public Sub RunAdvancedMagicSimulation()
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    
    ' 初始化系统
    Call InitializeSimulationSystem
    
    ' 获取用户配置
    Dim simulationCount As Long
    Dim initialMagicCount As Long
    simulationCount = InputBox("请输入模拟次数（建议1000-10000）:", "模拟配置", "1000")
    initialMagicCount = InputBox("请输入每个玩家的初始T1魔法数量:", "初始配置", "20")
    
    If simulationCount <= 0 Or initialMagicCount <= 0 Then
        MsgBox "配置无效，模拟取消。", vbExclamation
        GoTo Cleanup
    End If
    
    ' 运行批量模拟
    Dim aggregatedResults As Object
    Set aggregatedResults = RunBatchSimulation(simulationCount, initialMagicCount)
    
    ' 输出结果
    Call OutputSimulationResults(aggregatedResults, simulationCount)
    
    MsgBox "模拟完成！结果已输出到统计数据工作表。", vbInformation
    
Cleanup:
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    MsgBox "模拟过程中发生错误: " & Err.Description & " (错误号: " & Err.Number & ")", vbCritical
End Sub

' 初始化模拟系统
Private Sub InitializeSimulationSystem()
    Set g_SynthesisRules = CreateObject("Scripting.Dictionary")
    Set g_MagicTierMap = CreateObject("Scripting.Dictionary")
    Set g_TierPriority = CreateObject("Scripting.Dictionary")
    Set g_Statistics = CreateObject("Scripting.Dictionary")
    
    ' 设置等级优先级（数字越大优先级越高）
    g_TierPriority.Add "T5->T6", 5
    g_TierPriority.Add "T4->T5", 4
    g_TierPriority.Add "T3->T4", 3
    g_TierPriority.Add "T2->T3", 2
    g_TierPriority.Add "T1->T2", 1
    
    ' 加载合成规则
    Call LoadSynthesisRules
    
    ' 初始化魔法等级映射
    Call InitializeMagicTierMap
End Sub

' 从Excel加载合成规则
Private Sub LoadSynthesisRules()
    On Error GoTo ErrorHandler
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    Dim lastRow As Long
    lastRow = wsRules.Cells(wsRules.Rows.Count, 1).End(xlUp).Row
    
    Dim i As Long
    For i = 2 To lastRow ' 从第2行开始，跳过标题行
        Dim comboKey As String
        Dim resultMagic As String
        Dim tierTransition As String
        
        comboKey = Trim(wsRules.Cells(i, 1).Value)
        resultMagic = Trim(wsRules.Cells(i, 2).Value)
        tierTransition = Trim(wsRules.Cells(i, 3).Value)
        
        If comboKey <> "" And resultMagic <> "" Then
            ' 存储合成规则
            g_SynthesisRules.Add comboKey, Array(resultMagic, tierTransition)
            
            ' 同时存储反向组合（A+B = B+A）
            Dim reversedKey As String
            reversedKey = GetReversedComboKey(comboKey)
            If reversedKey <> comboKey And Not g_SynthesisRules.Exists(reversedKey) Then
                g_SynthesisRules.Add reversedKey, Array(resultMagic, tierTransition)
            End If
        End If
    Next i
    
    Exit Sub
    
ErrorHandler:
    MsgBox "加载合成规则时发生错误: " & Err.Description, vbCritical
End Sub

' 获取反向组合键
Private Function GetReversedComboKey(comboKey As String) As String
    Dim parts As Variant
    parts = Split(comboKey, " + ")
    If UBound(parts) = 1 Then
        GetReversedComboKey = parts(1) & " + " & parts(0)
    Else
        GetReversedComboKey = comboKey
    End If
End Function

' 初始化魔法等级映射
Private Sub InitializeMagicTierMap()
    ' T1魔法标签
    Dim t1Tags As Variant
    t1Tags = Array("火焰", "寒冰", "风暴", "大地", "生命", "死亡", "心灵", "灵魂")
    
    ' T2魔法标签
    Dim t2Tags As Variant
    t2Tags = Array("创造", "毁灭", "守护", "侵蚀", "扭曲", "净化")
    
    ' T3魔法标签
    Dim t3Tags As Variant
    t3Tags = Array("物质", "能量", "精神", "虚空")
    
    ' T4魔法标签
    Dim t4Tags As Variant
    t4Tags = Array("因果", "循环", "平衡")
    
    ' T5魔法标签
    Dim t5Tags As Variant
    t5Tags = Array("存在", "非在")
    
    ' T6魔法标签
    Dim t6Tags As Variant
    t6Tags = Array("奇点")
    
    ' 建立映射
    Call AddTagsToTierMap(t1Tags, 1)
    Call AddTagsToTierMap(t2Tags, 2)
    Call AddTagsToTierMap(t3Tags, 3)
    Call AddTagsToTierMap(t4Tags, 4)
    Call AddTagsToTierMap(t5Tags, 5)
    Call AddTagsToTierMap(t6Tags, 6)
End Sub

' 添加标签到等级映射
Private Sub AddTagsToTierMap(tags As Variant, tier As Integer)
    Dim i As Integer
    For i = LBound(tags) To UBound(tags)
        g_MagicTierMap.Add tags(i), tier
    Next i
End Sub

' 运行批量模拟
Private Function RunBatchSimulation(simulationCount As Long, initialMagicCount As Long) As Object
    Dim aggregatedResults As Object
    Set aggregatedResults = CreateObject("Scripting.Dictionary")
    
    Dim i As Long
    For i = 1 To simulationCount
        ' 显示进度
        If i Mod 100 = 0 Then
            Application.StatusBar = "模拟进度: " & i & "/" & simulationCount
        End If
        
        ' 运行单次模拟
        Dim singleResult As Object
        Set singleResult = SimulateSinglePlayer(initialMagicCount)
        
        ' 汇总结果
        Call AggregateResults(aggregatedResults, singleResult)
    Next i
    
    Application.StatusBar = False
    Set RunBatchSimulation = aggregatedResults
End Function

' 模拟单个玩家
Private Function SimulateSinglePlayer(initialMagicCount As Long) As Object
    Dim playerPool As Object
    Set playerPool = CreateObject("Scripting.Dictionary")
    
    Dim synthesisStats As Object
    Set synthesisStats = CreateObject("Scripting.Dictionary")
    
    ' 初始化玩家魔法池
    Call InitializePlayerPool(playerPool, initialMagicCount)
    
    ' 执行合成直到获得T6魔法或无法继续合成
    Dim hasT6Magic As Boolean
    Dim maxIterations As Long
    Dim currentIteration As Long
    
    hasT6Magic = False
    maxIterations = 1000 ' 防止无限循环
    currentIteration = 0
    
    Do While Not hasT6Magic And currentIteration < maxIterations
        currentIteration = currentIteration + 1
        
        ' 检查是否已有T6魔法
        If HasMagicOfTier(playerPool, 6) Then
            hasT6Magic = True
            Exit Do
        End If
        
        ' 寻找最佳合成选项
        Dim bestSynthesis As Variant
        bestSynthesis = FindBestSynthesis(playerPool)
        
        If IsEmpty(bestSynthesis) Then
            ' 无法继续合成
            Exit Do
        End If
        
        ' 执行合成
        Call ExecuteSynthesis(playerPool, bestSynthesis, synthesisStats)
    Loop
    
    ' 返回结果
    Dim result As Object
    Set result = CreateObject("Scripting.Dictionary")
    result.Add "FinalPool", playerPool
    result.Add "SynthesisStats", synthesisStats
    result.Add "HasT6", hasT6Magic
    result.Add "Iterations", currentIteration
    
    Set SimulateSinglePlayer = result
End Function

' 初始化玩家魔法池
Private Sub InitializePlayerPool(playerPool As Object, initialMagicCount As Long)
    ' T1魔法标签和数量分布
    Dim t1Tags As Variant
    t1Tags = Array("火焰", "寒冰", "风暴", "大地", "生命", "死亡", "心灵", "灵魂")
    
    ' 每个标签有4个魔法
    Dim tagIndex As Integer
    Dim magicIndex As Integer
    Dim totalAdded As Long
    
    Randomize
    totalAdded = 0
    
    ' 随机分配初始魔法
    Do While totalAdded < initialMagicCount
        tagIndex = Int(Rnd() * (UBound(t1Tags) + 1))
        magicIndex = Int(Rnd() * 4) + 1 ' 1-4
        
        Dim magicName As String
        magicName = t1Tags(tagIndex) & magicIndex
        
        If playerPool.Exists(magicName) Then
            playerPool(magicName) = playerPool(magicName) + 1
        Else
            playerPool.Add magicName, 1
        End If
        
        totalAdded = totalAdded + 1
    Loop
End Sub

' 检查是否拥有指定等级的魔法
Private Function HasMagicOfTier(playerPool As Object, tier As Integer) As Boolean
    Dim key As Variant
    For Each key In playerPool.Keys
        If GetMagicTier(CStr(key)) = tier And playerPool(key) > 0 Then
            HasMagicOfTier = True
            Exit Function
        End If
    Next key
    HasMagicOfTier = False
End Function

' 获取魔法的等级
Private Function GetMagicTier(magicName As String) As Integer
    Dim tag As String
    tag = GetMagicTag(magicName)

    If g_MagicTierMap.Exists(tag) Then
        GetMagicTier = g_MagicTierMap(tag)
    Else
        GetMagicTier = 0 ' 未知魔法
    End If
End Function

' 从魔法名称提取标签
Private Function GetMagicTag(magicName As String) As String
    Dim i As Integer
    For i = Len(magicName) To 1 Step -1
        If Not IsNumeric(Mid(magicName, i, 1)) Then
            GetMagicTag = Left(magicName, i)
            Exit Function
        End If
    Next i
    GetMagicTag = magicName
End Function

' 寻找最佳合成选项
Private Function FindBestSynthesis(playerPool As Object) As Variant
    Dim possibleSyntheses As Object
    Set possibleSyntheses = CreateObject("Scripting.Dictionary")

    ' 遍历所有可能的魔法组合
    Dim keys As Variant
    keys = playerPool.Keys

    Dim i As Integer, j As Integer
    For i = 0 To UBound(keys)
        For j = i To UBound(keys)
            Dim magic1 As String, magic2 As String
            magic1 = CStr(keys(i))
            magic2 = CStr(keys(j))

            ' 检查是否有足够的魔法进行合成
            Dim count1 As Long, count2 As Long
            count1 = playerPool(magic1)
            count2 = playerPool(magic2)

            Dim canSynthesize As Boolean
            If magic1 = magic2 Then
                canSynthesize = count1 >= 2
            Else
                canSynthesize = count1 >= 1 And count2 >= 1
            End If

            If canSynthesize Then
                ' 检查合成规则
                Dim tag1 As String, tag2 As String
                tag1 = GetMagicTag(magic1)
                tag2 = GetMagicTag(magic2)

                ' 相同标签不能合成
                If tag1 <> tag2 Then
                    Dim comboKey As String
                    comboKey = tag1 & " + " & tag2

                    If g_SynthesisRules.Exists(comboKey) Then
                        Dim ruleInfo As Variant
                        ruleInfo = g_SynthesisRules(comboKey)

                        Dim resultMagic As String
                        Dim tierTransition As String
                        resultMagic = ruleInfo(0)
                        tierTransition = ruleInfo(1)

                        ' 计算优先级
                        Dim priority As Integer
                        If g_TierPriority.Exists(tierTransition) Then
                            priority = g_TierPriority(tierTransition)
                        Else
                            priority = 0
                        End If

                        ' 存储合成选项
                        Dim synthesisKey As String
                        synthesisKey = magic1 & "|" & magic2

                        Dim synthesisInfo As Variant
                        synthesisInfo = Array(magic1, magic2, resultMagic, tierTransition, priority)

                        If Not possibleSyntheses.Exists(synthesisKey) Then
                            possibleSyntheses.Add synthesisKey, synthesisInfo
                        End If
                    End If
                End If
            End If
        Next j
    Next i

    ' 选择优先级最高的合成
    If possibleSyntheses.Count = 0 Then
        FindBestSynthesis = Empty
        Exit Function
    End If

    Dim bestSynthesis As Variant
    Dim maxPriority As Integer
    maxPriority = -1

    Dim key As Variant
    For Each key In possibleSyntheses.Keys
        Dim synthesis As Variant
        synthesis = possibleSyntheses(key)

        If synthesis(4) > maxPriority Then
            maxPriority = synthesis(4)
            bestSynthesis = synthesis
        End If
    Next key

    FindBestSynthesis = bestSynthesis
End Function

' 执行合成
Private Sub ExecuteSynthesis(playerPool As Object, synthesis As Variant, synthesisStats As Object)
    Dim magic1 As String, magic2 As String, resultMagic As String, tierTransition As String
    magic1 = synthesis(0)
    magic2 = synthesis(1)
    resultMagic = synthesis(2)
    tierTransition = synthesis(3)

    ' 消耗材料
    playerPool(magic1) = playerPool(magic1) - 1
    If playerPool(magic1) <= 0 Then
        playerPool.Remove magic1
    End If

    If magic1 <> magic2 Then
        playerPool(magic2) = playerPool(magic2) - 1
        If playerPool(magic2) <= 0 Then
            playerPool.Remove magic2
        End If
    Else
        playerPool(magic1) = playerPool(magic1) - 1
        If playerPool(magic1) <= 0 Then
            playerPool.Remove magic1
        End If
    End If

    ' 添加结果魔法
    If playerPool.Exists(resultMagic) Then
        playerPool(resultMagic) = playerPool(resultMagic) + 1
    Else
        playerPool.Add resultMagic, 1
    End If

    ' 更新统计
    If synthesisStats.Exists(tierTransition) Then
        synthesisStats(tierTransition) = synthesisStats(tierTransition) + 1
    Else
        synthesisStats.Add tierTransition, 1
    End If
End Sub

' 汇总结果
Private Sub AggregateResults(aggregatedResults As Object, singleResult As Object)
    Dim finalPool As Object
    Set finalPool = singleResult("FinalPool")

    Dim synthesisStats As Object
    Set synthesisStats = singleResult("SynthesisStats")

    ' 汇总最终魔法池
    If Not aggregatedResults.Exists("FinalPools") Then
        aggregatedResults.Add "FinalPools", CreateObject("Scripting.Dictionary")
    End If

    Dim finalPools As Object
    Set finalPools = aggregatedResults("FinalPools")

    Dim key As Variant
    For Each key In finalPool.Keys
        If finalPools.Exists(key) Then
            finalPools(key) = finalPools(key) + finalPool(key)
        Else
            finalPools.Add key, finalPool(key)
        End If
    Next key

    ' 汇总合成统计
    If Not aggregatedResults.Exists("SynthesisStats") Then
        aggregatedResults.Add "SynthesisStats", CreateObject("Scripting.Dictionary")
    End If

    Dim aggSynthesisStats As Object
    Set aggSynthesisStats = aggregatedResults("SynthesisStats")

    For Each key In synthesisStats.Keys
        If aggSynthesisStats.Exists(key) Then
            aggSynthesisStats(key) = aggSynthesisStats(key) + synthesisStats(key)
        Else
            aggSynthesisStats.Add key, synthesisStats(key)
        End If
    Next key

    ' 汇总成功率
    If Not aggregatedResults.Exists("SuccessCount") Then
        aggregatedResults.Add "SuccessCount", 0
    End If

    If singleResult("HasT6") Then
        aggregatedResults("SuccessCount") = aggregatedResults("SuccessCount") + 1
    End If

    ' 汇总迭代次数
    If Not aggregatedResults.Exists("TotalIterations") Then
        aggregatedResults.Add "TotalIterations", 0
    End If

    aggregatedResults("TotalIterations") = aggregatedResults("TotalIterations") + singleResult("Iterations")
End Sub

' 输出模拟结果
Private Sub OutputSimulationResults(aggregatedResults As Object, simulationCount As Long)
    On Error GoTo ErrorHandler

    Dim wsStats As Worksheet
    Set wsStats = ThisWorkbook.Sheets("统计数据")

    ' 清空现有数据
    wsStats.Range("A1:J1000").ClearContents

    ' 输出标题
    wsStats.Range("A1").Value = "高级魔法合成模拟结果"
    wsStats.Range("A2").Value = "模拟次数: " & simulationCount
    wsStats.Range("A3").Value = "成功获得T6魔法次数: " & aggregatedResults("SuccessCount")
    wsStats.Range("A4").Value = "成功率: " & Format(aggregatedResults("SuccessCount") / simulationCount, "0.00%")
    wsStats.Range("A5").Value = "平均合成轮数: " & Format(aggregatedResults("TotalIterations") / simulationCount, "0.0")

    ' 输出合成统计
    wsStats.Range("A7").Value = "合成阶级统计:"
    wsStats.Range("A8").Value = "阶级转换"
    wsStats.Range("B8").Value = "总次数"
    wsStats.Range("C8").Value = "平均次数"
    wsStats.Range("D8").Value = "占比"

    Dim row As Long
    row = 9

    If aggregatedResults.Exists("SynthesisStats") Then
        Dim synthesisStats As Object
        Set synthesisStats = aggregatedResults("SynthesisStats")

        Dim totalSyntheses As Long
        totalSyntheses = 0

        Dim key As Variant
        For Each key In synthesisStats.Keys
            totalSyntheses = totalSyntheses + synthesisStats(key)
        Next key

        For Each key In synthesisStats.Keys
            wsStats.Cells(row, 1).Value = key
            wsStats.Cells(row, 2).Value = synthesisStats(key)
            wsStats.Cells(row, 3).Value = Format(synthesisStats(key) / simulationCount, "0.0")
            wsStats.Cells(row, 4).Value = Format(synthesisStats(key) / totalSyntheses, "0.00%")
            row = row + 1
        Next key
    End If

    ' 输出最终魔法池统计
    row = row + 2
    wsStats.Cells(row, 1).Value = "最终魔法池统计:"
    row = row + 1
    wsStats.Cells(row, 1).Value = "魔法名称"
    wsStats.Cells(row, 2).Value = "总数量"
    wsStats.Cells(row, 3).Value = "平均数量"
    wsStats.Cells(row, 4).Value = "等级"
    row = row + 1

    If aggregatedResults.Exists("FinalPools") Then
        Dim finalPools As Object
        Set finalPools = aggregatedResults("FinalPools")

        ' 按等级排序输出
        Dim tierGroups(1 To 6) As Object
        Dim i As Integer
        For i = 1 To 6
            Set tierGroups(i) = CreateObject("Scripting.Dictionary")
        Next i

        For Each key In finalPools.Keys
            Dim tier As Integer
            tier = GetMagicTier(CStr(key))
            If tier >= 1 And tier <= 6 Then
                tierGroups(tier).Add key, finalPools(key)
            End If
        Next key

        For i = 1 To 6
            If tierGroups(i).Count > 0 Then
                For Each key In tierGroups(i).Keys
                    wsStats.Cells(row, 1).Value = key
                    wsStats.Cells(row, 2).Value = tierGroups(i)(key)
                    wsStats.Cells(row, 3).Value = Format(tierGroups(i)(key) / simulationCount, "0.0")
                    wsStats.Cells(row, 4).Value = "T" & i
                    row = row + 1
                Next key
            End If
        Next i
    End If

    ' 格式化表格
    Call FormatResultsTable(wsStats)

    Exit Sub

ErrorHandler:
    MsgBox "输出结果时发生错误: " & Err.Description, vbCritical
End Sub

' 格式化结果表格
Private Sub FormatResultsTable(ws As Worksheet)
    On Error Resume Next

    ' 设置标题格式
    With ws.Range("A1")
        .Font.Size = 14
        .Font.Bold = True
    End With

    ' 设置表头格式
    With ws.Range("A8:D8")
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
        .Borders.LineStyle = xlContinuous
    End With

    ' 自动调整列宽
    ws.Columns("A:D").AutoFit

    On Error GoTo 0
End Sub

' 快速测试函数（用于调试）
Public Sub QuickTest()
    On Error GoTo ErrorHandler

    Application.ScreenUpdating = False

    ' 初始化系统
    Call InitializeSimulationSystem

    ' 运行少量模拟进行测试
    Dim aggregatedResults As Object
    Set aggregatedResults = RunBatchSimulation(10, 20)

    ' 输出结果
    Call OutputSimulationResults(aggregatedResults, 10)

    MsgBox "快速测试完成！", vbInformation

    Application.ScreenUpdating = True
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "快速测试失败: " & Err.Description, vbCritical
End Sub

' 显示合成规则（用于调试）
Public Sub ShowSynthesisRules()
    Call InitializeSimulationSystem

    Dim msg As String
    msg = "已加载的合成规则数量: " & g_SynthesisRules.Count & vbCrLf & vbCrLf

    Dim count As Integer
    count = 0
    Dim key As Variant
    For Each key In g_SynthesisRules.Keys
        If count < 10 Then ' 只显示前10条
            Dim ruleInfo As Variant
            ruleInfo = g_SynthesisRules(key)
            msg = msg & key & " -> " & ruleInfo(0) & " (" & ruleInfo(1) & ")" & vbCrLf
            count = count + 1
        Else
            msg = msg & "... 还有 " & (g_SynthesisRules.Count - 10) & " 条规则"
            Exit For
        End If
    Next key

    MsgBox msg, vbInformation, "合成规则预览"
End Sub

' 创建预设魔法池（用于特定测试）
Public Sub CreatePresetMagicPool()
    On Error GoTo ErrorHandler

    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.Sheets("魔法模拟器")

    ' 清空现有魔法池
    wsSim.Range("H4:J30").ClearContents

    ' 创建一个平衡的T1魔法池
    Dim t1Tags As Variant
    t1Tags = Array("火焰", "寒冰", "风暴", "大地", "生命", "死亡", "心灵", "灵魂")

    Dim row As Long
    row = 4

    Dim i As Integer, j As Integer
    For i = LBound(t1Tags) To UBound(t1Tags)
        For j = 1 To 4 ' 每个标签4个魔法
            If row <= 30 Then
                wsSim.Cells(row, 8).Value = t1Tags(i) & j
                row = row + 1
            End If
        Next j
    Next i

    MsgBox "预设魔法池已创建！包含每种T1魔法各1个。", vbInformation

    Exit Sub

ErrorHandler:
    MsgBox "创建预设魔法池时发生错误: " & Err.Description, vbCritical
End Sub

' 分析当前魔法池的合成潜力
Public Sub AnalyzeMagicPool()
    On Error GoTo ErrorHandler

    Call InitializeSimulationSystem

    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.Sheets("魔法模拟器")

    ' 读取当前魔法池
    Dim playerPool As Object
    Set playerPool = CreateObject("Scripting.Dictionary")

    Dim cell As Range
    For Each cell In wsSim.Range("H4:J30")
        If Trim(cell.Value) <> "" Then
            Dim magicName As String
            magicName = Trim(cell.Value)

            If playerPool.Exists(magicName) Then
                playerPool(magicName) = playerPool(magicName) + 1
            Else
                playerPool.Add magicName, 1
            End If
        End If
    Next cell

    If playerPool.Count = 0 Then
        MsgBox "魔法池为空！", vbExclamation
        Exit Sub
    End If

    ' 分析合成潜力
    Dim analysis As String
    analysis = "魔法池分析结果:" & vbCrLf & vbCrLf
    analysis = analysis & "总魔法数量: " & GetTotalMagicCount(playerPool) & vbCrLf

    ' 按等级统计
    Dim tierCounts(1 To 6) As Long
    Dim key As Variant
    For Each key In playerPool.Keys
        Dim tier As Integer
        tier = GetMagicTier(CStr(key))
        If tier >= 1 And tier <= 6 Then
            tierCounts(tier) = tierCounts(tier) + playerPool(key)
        End If
    Next key

    Dim i As Integer
    For i = 1 To 6
        If tierCounts(i) > 0 Then
            analysis = analysis & "T" & i & " 魔法: " & tierCounts(i) & " 个" & vbCrLf
        End If
    Next i

    ' 检查可能的合成
    Dim possibleSyntheses As Long
    possibleSyntheses = CountPossibleSyntheses(playerPool)
    analysis = analysis & vbCrLf & "可执行的合成数量: " & possibleSyntheses

    MsgBox analysis, vbInformation, "魔法池分析"

    Exit Sub

ErrorHandler:
    MsgBox "分析魔法池时发生错误: " & Err.Description, vbCritical
End Sub

' 获取魔法池中的总魔法数量
Private Function GetTotalMagicCount(playerPool As Object) As Long
    Dim total As Long
    total = 0

    Dim key As Variant
    For Each key In playerPool.Keys
        total = total + playerPool(key)
    Next key

    GetTotalMagicCount = total
End Function

' 计算可能的合成数量
Private Function CountPossibleSyntheses(playerPool As Object) As Long
    Dim count As Long
    count = 0

    Dim keys As Variant
    keys = playerPool.Keys

    Dim i As Integer, j As Integer
    For i = 0 To UBound(keys)
        For j = i To UBound(keys)
            Dim magic1 As String, magic2 As String
            magic1 = CStr(keys(i))
            magic2 = CStr(keys(j))

            ' 检查是否有足够的魔法进行合成
            Dim count1 As Long, count2 As Long
            count1 = playerPool(magic1)
            count2 = playerPool(magic2)

            Dim canSynthesize As Boolean
            If magic1 = magic2 Then
                canSynthesize = count1 >= 2
            Else
                canSynthesize = count1 >= 1 And count2 >= 1
            End If

            If canSynthesize Then
                ' 检查合成规则
                Dim tag1 As String, tag2 As String
                tag1 = GetMagicTag(magic1)
                tag2 = GetMagicTag(magic2)

                ' 相同标签不能合成
                If tag1 <> tag2 Then
                    Dim comboKey As String
                    comboKey = tag1 & " + " & tag2

                    If g_SynthesisRules.Exists(comboKey) Then
                        count = count + 1
                    End If
                End If
            End If
        Next j
    Next i

    CountPossibleSyntheses = count
End Function
