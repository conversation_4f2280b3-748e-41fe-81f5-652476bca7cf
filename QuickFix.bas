Option Explicit

'==============================================================================
' 快速修复脚本
' 用于诊断和修复合成规则问题
'==============================================================================

' 快速诊断问题
Public Sub QuickDiagnosis()
    On Error GoTo ErrorHandler
    
    MsgBox "开始快速诊断...", vbInformation
    
    ' 1. 测试规则加载
    Call InitializeOptimizedSystem
    
    Dim diagnosis As String
    diagnosis = "=== 快速诊断报告 ===" & vbCrLf & vbCrLf
    
    ' 检查规则数量
    diagnosis = diagnosis & "1. 规则加载检查:" & vbCrLf
    diagnosis = diagnosis & "   总规则数量: " & g_OptimizedRules.Count & vbCrLf & vbCrLf
    
    ' 检查关键规则
    diagnosis = diagnosis & "2. 关键规则检查:" & vbCrLf
    
    ' T3->T4规则检查
    If g_OptimizedRules.Exists("物质 + 能量") Then
        Dim ruleInfo As Variant
        ruleInfo = g_OptimizedRules("物质 + 能量")
        diagnosis = diagnosis & "   ✅ T3->T4规则存在: 物质 + 能量 -> " & ruleInfo(0) & vbCrLf
    Else
        diagnosis = diagnosis & "   ❌ T3->T4规则缺失: 物质 + 能量" & vbCrLf
    End If
    
    ' T4->T5规则检查
    If g_OptimizedRules.Exists("因果 + 平衡") Then
        ruleInfo = g_OptimizedRules("因果 + 平衡")
        diagnosis = diagnosis & "   ✅ T4->T5规则存在: 因果 + 平衡 -> " & ruleInfo(0) & vbCrLf
    Else
        diagnosis = diagnosis & "   ❌ T4->T5规则缺失: 因果 + 平衡" & vbCrLf
    End If
    
    ' T5->T6规则检查
    If g_OptimizedRules.Exists("存在 + 非在") Then
        ruleInfo = g_OptimizedRules("存在 + 非在")
        diagnosis = diagnosis & "   ✅ T5->T6规则存在: 存在 + 非在 -> " & ruleInfo(0) & vbCrLf
    Else
        diagnosis = diagnosis & "   ❌ T5->T6规则缺失: 存在 + 非在" & vbCrLf
    End If
    
    diagnosis = diagnosis & vbCrLf
    
    ' 3. 测试魔法池
    diagnosis = diagnosis & "3. 魔法池检查:" & vbCrLf
    Dim testPool As Object
    Set testPool = ReadMagicPoolFromExcel()
    
    If testPool.Count > 0 Then
        diagnosis = diagnosis & "   ✅ 魔法池已加载: " & testPool.Count & " 种魔法" & vbCrLf
        
        ' 显示前几个魔法
        Dim count As Integer
        count = 0
        Dim key As Variant
        For Each key In testPool.Keys
            If count < 5 Then
                diagnosis = diagnosis & "   - " & key & " x" & testPool(key) & vbCrLf
                count = count + 1
            End If
        Next key
    Else
        diagnosis = diagnosis & "   ❌ 魔法池为空" & vbCrLf
    End If
    
    MsgBox diagnosis, vbInformation, "诊断结果"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "诊断过程中发生错误: " & Err.Description, vbCritical
End Sub

' 修复合成规则格式
Public Sub FixRuleFormat()
    On Error GoTo ErrorHandler
    
    Dim result As VbMsgBoxResult
    result = MsgBox("这将修复合成规则工作表中的魔法名称格式。" & vbCrLf & vbCrLf & _
                   "会将 '因果' 改为 '因果1'，'循环' 改为 '循环1' 等。" & vbCrLf & vbCrLf & _
                   "确定要继续吗？", vbYesNo + vbQuestion, "修复规则格式")
    
    If result = vbNo Then Exit Sub
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    Dim lastRow As Long
    lastRow = wsRules.Cells(wsRules.Rows.Count, 1).End(xlUp).Row
    
    Dim fixCount As Integer
    fixCount = 0
    
    Dim i As Long
    For i = 2 To lastRow
        Dim resultMagic As String
        resultMagic = Trim(wsRules.Cells(i, 2).Value)
        
        If resultMagic <> "" Then
            ' 检查是否需要添加数字后缀
            Dim lastChar As String
            If Len(resultMagic) > 0 Then
                lastChar = Right(resultMagic, 1)
                
                ' 如果最后一个字符不是数字，添加"1"
                If Not IsNumeric(lastChar) Then
                    wsRules.Cells(i, 2).Value = resultMagic & "1"
                    fixCount = fixCount + 1
                End If
            End If
        End If
    Next i
    
    MsgBox "修复完成！" & vbCrLf & vbCrLf & _
           "修复了 " & fixCount & " 个魔法名称格式。", vbInformation, "修复结果"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "修复过程中发生错误: " & Err.Description, vbCritical
End Sub

' 测试单个合成
Public Sub TestSingleSynthesis()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 创建测试魔法池
    Dim testPool As Object
    Set testPool = CreateObject("Scripting.Dictionary")
    
    ' 添加一些T3魔法进行测试
    testPool.Add "物质1", 1
    testPool.Add "能量1", 1
    
    MsgBox "测试合成:" & vbCrLf & vbCrLf & _
           "魔法池: 物质1 x1, 能量1 x1" & vbCrLf & _
           "期望结果: 因果1", vbInformation
    
    ' 寻找合成选项
    Dim bestSynthesis As Variant
    bestSynthesis = FindOptimizedBestSynthesis(testPool)
    
    If IsEmpty(bestSynthesis) Then
        MsgBox "❌ 测试失败！" & vbCrLf & vbCrLf & _
               "无法找到 物质1 + 能量1 的合成规则。" & vbCrLf & vbCrLf & _
               "请运行 FixRuleFormat 修复规则格式。", vbExclamation
    Else
        MsgBox "✅ 测试成功！" & vbCrLf & vbCrLf & _
               "找到合成: " & bestSynthesis(0) & " + " & bestSynthesis(1) & " -> " & bestSynthesis(2) & vbCrLf & _
               "类型: " & bestSynthesis(3), vbInformation
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "测试过程中发生错误: " & Err.Description, vbCritical
End Sub

' 显示所有高等级规则
Public Sub ShowHighTierRules()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    Dim report As String
    report = "=== 高等级合成规则 ===" & vbCrLf & vbCrLf
    
    ' 按等级分组显示
    Dim tierNames As Variant
    tierNames = Array("Tier 3 -> Tier 4", "Tier 4 -> Tier 5", "Tier 5 -> Tier 6")
    
    Dim tierIndex As Integer
    For tierIndex = LBound(tierNames) To UBound(tierNames)
        Dim tierName As String
        tierName = tierNames(tierIndex)
        
        report = report & "【" & tierName & "】" & vbCrLf
        
        Dim ruleCount As Integer
        ruleCount = 0
        
        Dim key As Variant
        For Each key In g_OptimizedRules.Keys
            Dim ruleInfo As Variant
            ruleInfo = g_OptimizedRules(key)
            
            If ruleInfo(1) = tierName Then
                report = report & "  " & key & " -> " & ruleInfo(0) & vbCrLf
                ruleCount = ruleCount + 1
            End If
        Next key
        
        If ruleCount = 0 Then
            report = report & "  ❌ 无规则" & vbCrLf
        End If
        
        report = report & vbCrLf
    Next tierIndex
    
    MsgBox report, vbInformation, "高等级规则"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "显示规则时发生错误: " & Err.Description, vbCritical
End Sub

' 一键修复和测试
Public Sub OneClickFix()
    On Error GoTo ErrorHandler
    
    MsgBox "开始一键修复和测试...", vbInformation
    
    ' 1. 修复规则格式
    Call FixRuleFormat
    
    ' 2. 测试规则加载
    Call TestOptimizedRules
    
    ' 3. 测试单个合成
    Call TestSingleSynthesis
    
    MsgBox "一键修复和测试完成！" & vbCrLf & vbCrLf & _
           "现在可以尝试运行 QuickOptimizedSimulation 进行完整测试。", vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "一键修复过程中发生错误: " & Err.Description, vbCritical
End Sub
